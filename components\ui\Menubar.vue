<script setup lang="js">
import { computed } from 'vue'
import { cn } from '@/lib/utils'

const props = withDefaults(defineProps({
  class: {
    type: String,
    default: ''
  }
}), {})

const menubarClass = computed(() => 
  cn(
    'flex h-10 items-center space-x-1 rounded-md border bg-background p-1',
    props.class
  )
)
</script>

<template>
  <div :class="menubarClass">
    <slot />
  </div>
</template>
